using System.Diagnostics.Metrics;
using static Application.Instrumentation.Metrics.StabilityAnalysisMetricsConstants;

namespace Application.Instrumentation.Metrics;

public class StabilityAnalysisMetricHandler
{
    /// <summary>
    /// Number of stability analysis sent to Slide2 API that failed and haven't
    /// returned results after all the retries.
    /// </summary>
    private readonly Counter<int> _slide2AnalysisFailed;

    public StabilityAnalysisMetricHandler(IMeterFactory meterFactory)
    {
        var meter = meterFactory.Create(
            ApplicationConstants.ServiceName,
            ApplicationConstants.Version);
        
        _slide2AnalysisFailed = meter.CreateCounter<int>(
            Names.Slide2AnalysisFailed,
            MeasurementUnits.Occurrences);
    }

    public void IncrementSlide2AnalysisFailed(int quantity = 1)
    {
        _slide2AnalysisFailed.Add(quantity);
    }
}