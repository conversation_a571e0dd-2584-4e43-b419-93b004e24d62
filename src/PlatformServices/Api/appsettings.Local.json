{"Apis": {"Clients": {"Url": "https://localhost:5100"}, "Keycloak": {"Url": "https://logisoil.eastus2.cloudapp.azure.com/auth", "Realm": "logisoil"}, "Slide2": {"Url": "http://logisoil.com:51234"}, "Transcription": {"Url": "https://func-ivory-shared-prd.azurewebsites.net", "ApiKey": "", "TimeoutInSeconds": 60}, "TextGeneration": {"Url": "https://func-ivory-shared-prd.azurewebsites.net", "ApiKey": "", "TimeoutInSeconds": 60}}, "Auth": {"Workflows": {"ClientSecret": "********************************", "ClientId": "logisoil-workflows-api"}}, "AutomatedReading": {"DeleteReadingsCronExpression": "0 0/30 * * * ?", "ImportReadingsCronExpression": "0 0/2 * * * ?", "SftpFilesPurgeDays": 30}, "AzureMonitor": {"ConnectionString": "InstrumentationKey=b26e5dac-5e4f-480d-840e-5b0ed57c6898;IngestionEndpoint=https://westus2-1.in.applicationinsights.azure.com/;LiveEndpoint=https://westus2.livediagnostics.monitor.azure.com/;ApplicationId=26774bf7-3738-4039-a2d8-2786764c9d92", "SamplingRatio": 0.5, "EnableLiveMetrics": false, "DisableOfflineStorage": true}, "BlobStorage": {"AccountName": "stwalmdev", "AccountKey": "****************************************************************************************", "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=stwalmdev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ClientsContainer": "clients"}, "Database": {"Workflows": {"ConnectionString": "Server=tcp:sql-walm-logisoil-dev.database.windows.net,1433;Initial Catalog=sqldb-logisoil-workflows-dev;Persist Security Info=False;User ID=sqladmin;Password=**************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Elsa": "Warning", "FluentMigrator": "Warning", "MassTransit": "Warning", "Quartz": "Warning", "Rebus": "Warning"}, "Debug": {"LogLevel": {"Default": "Information", "Microsoft.Hosting": "Trace"}}, "EventSource": {"LogLevel": {"Default": "Information"}}}, "MessageBroker": {"Workflows": {"ConnectionString": "rabbitmq://guest:guest@localhost"}}, "ScheduledReport": {"CronExpression": "0 0/1 * * * ?", "HoursToLookBack": 1}, "Sftp": {"Host": "*************", "Port": 2022, "UserName": "logisoil-workflows", "Password": "YDu^Af9b6DpH^>7!Gu>H"}, "Smtp": {"Host": "smtp.sendgrid.net", "Port": 587, "DefaultSender": "<EMAIL>", "UserName": "apikey", "Password": "*********************************************************************"}, "WebScraper": {"Username": "webscraper.relatorios", "Password": "Ivory@2025", "LogisoilFrontendUri": "https://app-logisoil-web-dev3.azurewebsites.net", "LogisoilAuthUri": "https://logisoil.eastus2.cloudapp.azure.com/auth/realms/logisoil/protocol/openid-connect/auth", "ViewportSize": {"Width": 1920, "Height": 1080}}, "WorkflowCleanup": {"TimeToLive": "00:01:00:00", "SweepInterval": "00:00:00:30", "BatchSize": 30}}