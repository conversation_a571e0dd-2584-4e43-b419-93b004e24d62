{"Apis": {"Keycloak": {"Url": "https://logisoil.eastus2.cloudapp.azure.com/auth", "Realm": "logisoil"}, "Clients": {"Url": "https://logisoil.eastus2.cloudapp.azure.com/clients-api"}, "Slide2": {"Url": "http://logisoil.com:51234"}, "Transcription": {"Url": "https://func-ivory-shared-prd.azurewebsites.net", "ApiKey": "", "TimeoutInSeconds": 60}, "TextGeneration": {"Url": "https://func-ivory-shared-prd.azurewebsites.net", "ApiKey": "", "TimeoutInSeconds": 60}}, "AutomatedReading": {"DeleteReadingsCronExpression": "0 0/30 * * * ?", "ImportReadingsCronExpression": "0 0/2 * * * ?", "SftpFilesPurgeDays": 30}, "BlobStorage": {"AccountName": "stwalmdev", "AccountKey": "****************************************************************************************", "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=stwalmdev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ClientsContainer": "clients"}, "ScheduledReport": {"CronExpression": "0 0/1 * * * ?", "HoursToLookBack": 1}, "Sftp": {"Host": "*************", "Port": 2022, "UserName": "logisoil-workflows", "Password": "YDu^Af9b6DpH^>7!Gu>H"}, "Smtp": {"Host": "smtp.sendgrid.net", "Port": 587, "DefaultSender": "<EMAIL>", "UserName": "apikey"}, "Vault": {"Url": "https://kv-walm-dev.vault.azure.net/", "TenantId": "f8f03f04-d496-4994-a0a7-f4f8e4d37148", "ClientId": "6d4c2a99-3ce1-4de4-8e22-ffe062c0cce5", "ClientSecret": "****************************************"}, "WebScraper": {"Username": "webscraper.relatorios", "Password": "Ivory@2024", "LogisoilFrontendUri": "https://app-logisoil-web-dev3.azurewebsites.net", "LogisoilAuthUri": "https://logisoil.eastus2.cloudapp.azure.com/auth/realms/logisoil/protocol/openid-connect/auth", "ViewportSize": {"Width": 1920, "Height": 1080}}, "WorkflowCleanup": {"TimeToLive": "01:00:00:00", "SweepInterval": "00:00:00:30", "BatchSize": 30}}