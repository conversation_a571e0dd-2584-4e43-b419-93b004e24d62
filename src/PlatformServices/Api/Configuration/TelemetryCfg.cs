using Application.Instrumentation;
using Application.Instrumentation.Metrics;
using Azure.Monitor.OpenTelemetry.AspNetCore;
using OpenTelemetry.Logs;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;

namespace Api.Configuration;

public static class TelemetryCfg
{
    public static IServiceCollection AddTelemetry(
        this IServiceCollection services,
        AzureMonitorOptions azureMonitorOptions)
    {
        services
            .AddMetrics()
            .AddSingleton<StabilityAnalysisMetricHandler>()
            .AddOpenTelemetry()
            .UseAzureMonitor(options =>
            {
                options.ConnectionString = azureMonitorOptions.ConnectionString;
                options.SamplingRatio = azureMonitorOptions.SamplingRatio;
                options.EnableLiveMetrics = azureMonitorOptions.EnableLiveMetrics;
                options.DisableOfflineStorage = azureMonitorOptions.DisableOfflineStorage;
            })
            .ConfigureResource(resourceBuilder =>
                resourceBuilder.AddService(
                    ApplicationConstants.ServiceName,
                    ApplicationConstants.Version)
            )
            .WithTracing(traceBuilder =>
            {
                traceBuilder
                    .SetSampler(new AlwaysOnSampler())
                    .AddAspNetCoreInstrumentation(options => options.Filter = GetExcludedRequests)
                    .AddOtlpExporter();
            })
            .WithMetrics(metricsBuilder =>
            {
                metricsBuilder
                    .AddMeter(ApplicationConstants.ServiceName)
                    .AddOtlpExporter();
            })
            .WithLogging(loggingBuilder =>
            {
                loggingBuilder
                    .AddConsoleExporter()
                    .AddOtlpExporter();
            });

        return services;
    }

    private static readonly Func<HttpContext, bool> GetExcludedRequests = (httpContext) =>
    {
        return !(httpContext.Request.Path.Value.EndsWith(".css") ||
                 httpContext.Request.Path.Value.EndsWith(".js") ||
                 httpContext.Request.Path.Value.EndsWith(".html") ||
                 httpContext.Request.Path.Value.EndsWith(".ico") ||
                 httpContext.Request.Method.Equals("OPTIONS") ||
                 httpContext.Request.Method.Equals("HEAD")) ||
               httpContext.Request.Path.Value.Equals("/health");
    };
}